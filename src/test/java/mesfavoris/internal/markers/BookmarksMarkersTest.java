package mesfavoris.internal.markers;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.intellij.testFramework.fixtures.BasePlatformTestCase;
import mesfavoris.BookmarksException;
import mesfavoris.IBookmarksMarkers;
import mesfavoris.bookmarktype.BookmarkMarker;
import mesfavoris.model.Bookmark;
import mesfavoris.model.BookmarkDatabase;
import mesfavoris.model.BookmarkFolder;
import mesfavoris.model.BookmarkId;
import mesfavoris.service.BookmarksService;

import static mesfavoris.tests.commons.waits.Waiter.waitUntil;
import static org.assertj.core.api.Assertions.assertThat;
import static mesfavoris.texteditor.TextEditorBookmarkProperties.PROP_WORKSPACE_PATH;
import static mesfavoris.texteditor.TextEditorBookmarkProperties.PROP_LINE_NUMBER;

public class BookmarksMarkersTest extends BasePlatformTestCase {
    private IBookmarksMarkers bookmarksMarkers;
    private BookmarkDatabase bookmarkDatabase;
    private BookmarkId rootFolderId;

    @Override
    protected void setUp() throws Exception {
        super.setUp();
        BookmarksService bookmarksService = getProject().getService(BookmarksService.class);
        this.bookmarkDatabase = bookmarksService.getBookmarkDatabase();
        this.bookmarksMarkers = bookmarksService.getBookmarksMarkers();
        this.rootFolderId = bookmarkDatabase.getBookmarksTree().getRootFolder().getId();
    }

    @Override
    protected String getTestDataPath() {
        return "src/test/testData";
    }

	public void testMarkerAddedWhenBookmarkAdded() throws Exception {
		// Given
		myFixture.copyDirectoryToProject("bookmarkMarkersTest", "testMarkerAddedWhenBookmarkAdded");
		Bookmark bookmark = new Bookmark(new BookmarkId(), ImmutableMap.of(
				PROP_WORKSPACE_PATH, "testMarkerAddedWhenBookmarkAdded/file.txt",
				PROP_LINE_NUMBER, "0"
		));

		// When
		addBookmark(rootFolderId, bookmark);
		BookmarkMarker marker = waitUntil("Cannot find marker", () -> findBookmarkMarker(bookmark.getId()));

		// Then
		assertThat(marker).isNotNull();
		assertThat(Integer.parseInt(marker.getAttributes().get(BookmarkMarker.LINE_NUMBER))).isEqualTo(0);
	}

	public void testMarkerDeletedWhenBookmarkDeleted() throws Exception {
		// Given
		myFixture.copyDirectoryToProject("bookmarkMarkersTest", "testMarkerDeletedWhenBookmarkDeleted");
		Bookmark bookmark = new Bookmark(new BookmarkId(), ImmutableMap.of(
				PROP_WORKSPACE_PATH, "testMarkerDeletedWhenBookmarkDeleted/file.txt",
				PROP_LINE_NUMBER, "0"
		));
		addBookmark(rootFolderId, bookmark);
		waitUntil("Cannot find marker", () -> findBookmarkMarker(bookmark.getId()));

		// When
		deleteBookmark(bookmark.getId());

		// Then
		waitUntil("Marker not deleted", () -> findBookmarkMarker(bookmark.getId()) == null);
	}

	public void testMarkerDeletedWhenBookmarkParentDeletedRecursively() throws Exception {
		// Given
		myFixture.copyDirectoryToProject("bookmarkMarkersTest", "testMarkerDeletedWhenBookmarkParentDeletedRecursively");
		BookmarkFolder bookmarkFolder = new BookmarkFolder(new BookmarkId(), "folder");
		addBookmark(rootFolderId, bookmarkFolder);
		Bookmark bookmark = new Bookmark(new BookmarkId(), ImmutableMap.of(
				PROP_WORKSPACE_PATH, "testMarkerDeletedWhenBookmarkDeleted/file.txt",
				PROP_LINE_NUMBER, "0"
		));
		addBookmark(bookmarkFolder.getId(), bookmark);
		waitUntil("Cannot find marker", () -> findBookmarkMarker(bookmark.getId()));

		// When
		deleteBookmarkRecursively(bookmarkFolder.getId());

		// Then
		waitUntil("Marker not deleted", () -> findBookmarkMarker(bookmark.getId()) == null);
	}

	public void testMarkerModifiedWhenBookmarkChanged() throws Exception {
		// Given
		myFixture.copyDirectoryToProject("bookmarkMarkersTest", "testMarkerModifiedWhenBookmarkChanged");
		Bookmark bookmark = new Bookmark(new BookmarkId(), ImmutableMap.of(
				PROP_WORKSPACE_PATH, "testMarkerModifiedWhenBookmarkChanged/file.txt",
				PROP_LINE_NUMBER, "0"
		));
		addBookmark(rootFolderId, bookmark);
		waitUntil("Cannot find marker", () -> findBookmarkMarker(bookmark.getId()));

		// When
		modifyBookmark(bookmark.getId(), PROP_LINE_NUMBER, "1");

		// Then
		waitUntil("Marker not modified", () ->
				Integer.parseInt(findBookmarkMarker(bookmark.getId()).getAttributes().get(BookmarkMarker.LINE_NUMBER)) == 1
		);
	}

	public void testMarkerReplacedWhenBookmarkResourceChanged() throws Exception {
		// Given
		myFixture.copyDirectoryToProject("bookmarkMarkersTest", "testMarkerReplacedWhenBookmarkResourceChanged");
		Bookmark bookmark = new Bookmark(new BookmarkId(), ImmutableMap.of(
				PROP_WORKSPACE_PATH, "testMarkerReplacedWhenBookmarkResourceChanged/file.txt",
				PROP_LINE_NUMBER, "0"
		));
		addBookmark(rootFolderId, bookmark);
		waitUntil("Cannot find marker", () -> findBookmarkMarker(bookmark.getId()));

		// When
		modifyBookmark(bookmark.getId(), PROP_WORKSPACE_PATH, "testMarkerReplacedWhenBookmarkResourceChanged/file2.txt");

		// Then
		waitUntil("Marker not modified", () -> {
			BookmarkMarker m = findBookmarkMarker(bookmark.getId());
			return m != null && m.getResource().getPath().endsWith("/testMarkerReplacedWhenBookmarkResourceChanged/file2.txt");
		});
	}

	// Note: This test is not applicable in IntelliJ as project open/close behavior is different
	// and markers are managed differently than in Eclipse

	private void addBookmark(BookmarkId parentId, Bookmark bookmark) throws BookmarksException {
		bookmarkDatabase.modify(bookmarksTreeModifier ->
				bookmarksTreeModifier.addBookmarks(parentId, Lists.newArrayList(bookmark))
		);
	}

	private void deleteBookmark(BookmarkId bookmarkId) throws BookmarksException {
		bookmarkDatabase.modify(bookmarksTreeModifier -> bookmarksTreeModifier.deleteBookmark(bookmarkId, false));
	}

	private void deleteBookmarkRecursively(BookmarkId bookmarkId) throws BookmarksException {
		bookmarkDatabase.modify(bookmarksTreeModifier -> bookmarksTreeModifier.deleteBookmark(bookmarkId, true));
	}

	private void modifyBookmark(BookmarkId bookmarkId, String propertyName, String propertyValue) throws BookmarksException {
		bookmarkDatabase.modify(bookmarksTreeModifier ->
				bookmarksTreeModifier.setPropertyValue(bookmarkId, propertyName, propertyValue)
		);
	}

	private BookmarkMarker findBookmarkMarker(BookmarkId bookmarkId) {
		return bookmarksMarkers.getMarker(bookmarkId);
	}
}
