package mesfavoris.java;

import mesfavoris.model.Bookmark;

public class JavaBookmarkProperties {
	public static final String PROP_LINE_NUMBER_INSIDE_ELEMENT = "java.lineNumberInsideElement";
	public static final String PROP_JAVA_ELEMENT_NAME = "java.elementName";
	public static final String PROP_JAVA_DECLARING_TYPE = "java.declaringType";
	public static final String PROP_JAVA_ELEMENT_KIND = "java.elementKind";
	public static final String PROP_JAVA_METHOD_SIGNATURE = "java.methodSignature";
	public static final String PROP_JAVA_TYPE = "java.type";

	// Element kinds
	public static final String KIND_FIELD = "field";
	public static final String KIND_INITIALIZER = "initializer";
	public static final String KIND_METHOD = "method";
	public static final String KIND_INTERFACE = "interface";
	public static final String KIND_ENUM = "enum";
	public static final String KIND_ANNOTATION = "annotation";
	public static final String KIND_CLASS = "class";
	public static final String KIND_TYPE = "type";

	// Common properties
	public static final String PROPERTY_NAME = Bookmark.PROPERTY_NAME;
	public static final String PROPERTY_COMMENT = Bookmark.PROPERTY_COMMENT;
}