<idea-plugin>
    <actions>
        <action id="mesfavoris.actions.AddBookmarkAction" class="mesfavoris.internal.actions.AddBookmarkAction"
                text="Add Favori" description="Add a Bookmark">
            <keyboard-shortcut keymap="$default" first-keystroke="alt B"/>
            <add-to-group group-id="EditorGutterPopupMenu" anchor="first"/>
        </action>
        <action id="mesfavoris.actions.GotoBookmarkAction"
                class="mesfavoris.internal.actions.GotoBookmarkAction" text="Goto Favori" description="Goto Bookmark"/>
        <action id="mesfavoris.actions.ManagePlaceholdersAction"
                class="mesfavoris.internal.actions.ManagePlaceholdersAction" text="Manage Placeholders" description="Manage path placeholders with usage statistics"/>
        <group id="mesfavoris.PopupMenu" text="Tree Popup" popup="true">
            <action id="mesfavoris.actions.DeleteBookmarkAction" class="mesfavoris.internal.actions.DeleteBookmarkAction" text="Delete" description="Delete selected bookmarks">
                <keyboard-shortcut first-keystroke="DELETE" keymap="$default"/>
                <keyboard-shortcut first-keystroke="BACK_SPACE" keymap="$default"/>
            </action>
            <action id="mesfavoris.actions.NewBookmarkFolderAction" class="mesfavoris.internal.actions.NewBookmarkFolderAction" text="New Bookmark Folder" description="Create a new Bookmark Folder">
            </action>
            <action id="mesfavoris.actions.RenameBookmarkAction"  class="mesfavoris.internal.actions.RenameBookmarkAction" text="Rename" description="Rename Bookmark">
            </action>
            <action id="mesfavoris.actions.PasteBookmarkAction"  class="mesfavoris.internal.actions.PasteBookmarkAction" text="Paste" description="Paste" icon="AllIcons.Actions.MenuPaste">
                <keyboard-shortcut keymap="$default" first-keystroke="control V"/>
                <keyboard-shortcut keymap="Mac OS X 10.5+" first-keystroke="meta V"/>
            </action>
            <group id="mesfavoris.CopyPasteSpecial" text="Copy/Paste Special" description="Copy/Paste Special" popup="true">
                <action id="mesfavoris.actions.PasteAsSnippetAction" class="mesfavoris.internal.actions.PasteAsSnippetAction" text="Paste As Snippet" description="Paste clipboard content as a snippet bookmark">
                </action>
            </group>
        </group>
    </actions>
</idea-plugin>