<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin xmlns:xi="http://www.w3.org/2001/XInclude">
    <id>com.cchabanois.mesfavoris</id>
    <name>Mesfavoris</name>
    <vendor>cchabanois</vendor>

    <depends>com.intellij.modules.platform</depends>
    <depends>com.intellij.java</depends>

    <!-- Extension Points for Bookmark Types -->
    <extensionPoints>
        <!-- Bookmark Type Extension Point -->
        <extensionPoint name="bookmarkType" interface="mesfavoris.extensions.BookmarkTypeExtension" dynamic="true"/>
    </extensionPoints>

    <xi:include href="/META-INF/actions.xml" xpointer="xpointer(/idea-plugin/*)"/>
    <xi:include href="/META-INF/extensions.xml" xpointer="xpointer(/idea-plugin/*)"/>

    <resource-bundle>messages.MyBundle</resource-bundle>
    <applicationListeners>
        <listener class="mesfavoris.listeners.MyApplicationActivationListener" topic="com.intellij.openapi.application.ApplicationActivationListener"/>
    </applicationListeners>
</idea-plugin>
